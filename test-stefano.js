import WebSocket from 'ws';

console.log('🚀 Test GPS per utente Stefano (IMEI: 1234567890)');

const ws = new WebSocket('ws://localhost:8090/ws');

// Coordinate per un viaggio di test in Italia (Roma area)
const testRoute = [
  { lat: 41.9028, lng: 12.4964, speed: 0, status: 'Inizio' },        // Roma centro
  { lat: 41.9109, lng: 12.4818, speed: 35, status: 'GPS Fixed' },    // Vaticano
  { lat: 41.8986, lng: 12.4768, speed: 45, status: 'GPS Fixed' },    // Trastevere
  { lat: 41.8919, lng: 12.4824, speed: 25, status: 'GPS Fixed' },    // Testaccio
  { lat: 41.8902, lng: 12.4922, speed: 40, status: 'GPS Fixed' },    // Colosseo
  { lat: 41.9028, lng: 12.4964, speed: 0, status: 'Fine' }           // Ritorno al centro
];

let currentPosition = 0;
let intervalId;

ws.on('open', () => {
  console.log('✅ Connesso al server GPS');
  
  // Inizia la simulazione del viaggio
  console.log('🚗 Inizio simulazione viaggio per Stefano...');
  
  function sendNextPosition() {
    if (currentPosition < testRoute.length) {
      const position = testRoute[currentPosition];
      
      const message = {
        type: 'gps_data',
        data: {
          imei: '1234567890',
          timestamp: new Date().toISOString(),
          lat: position.lat,
          lng: position.lng,
          speed: position.speed,
          status: position.status,
          battery: Math.max(95 - currentPosition * 2, 70) // Batteria che diminuisce
        }
      };
      
      console.log(`📍 Posizione ${currentPosition + 1}/${testRoute.length}: ${position.lat}, ${position.lng} - ${position.status} - ${position.speed} km/h`);
      ws.send(JSON.stringify(message));
      
      currentPosition++;
      
      if (currentPosition < testRoute.length) {
        // Invia la prossima posizione dopo 3 secondi
        setTimeout(sendNextPosition, 3000);
      } else {
        console.log('🏁 Viaggio completato!');
        setTimeout(() => {
          console.log('🔚 Chiusura connessione');
          ws.close();
          process.exit(0);
        }, 2000);
      }
    }
  }
  
  // Inizia con la prima posizione
  sendNextPosition();
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log('📥 Risposta server:', message.type);
  } catch (error) {
    console.log('📥 Messaggio ricevuto:', data.toString());
  }
});

ws.on('close', () => {
  console.log('❌ Disconnesso dal server');
});

ws.on('error', (error) => {
  console.error('🚨 Errore WebSocket:', error.message);
  process.exit(1);
});

// Gestione interruzione
process.on('SIGINT', () => {
  console.log('\n🛑 Interruzione manuale');
  if (intervalId) clearInterval(intervalId);
  ws.close();
  process.exit(0);
});
