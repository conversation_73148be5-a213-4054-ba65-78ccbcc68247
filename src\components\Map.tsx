import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';
import { LatLngBounds, LatLngTuple } from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { GPSMessage, Voyage } from '../types';

// Fix for default markers in react-leaflet
import L from 'leaflet';
import icon from 'leaflet/dist/images/marker-icon.png';
import iconShadow from 'leaflet/dist/images/marker-shadow.png';

let DefaultIcon = L.divIcon({
  html: `<div style="background-color: #4ade80; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
  iconSize: [12, 12],
  iconAnchor: [6, 6],
});

let SelectedIcon = L.divIcon({
  html: `<div style="background-color: #ef4444; width: 16px; height: 16px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.4);"></div>`,
  iconSize: [16, 16],
  iconAnchor: [8, 8],
});

L.Marker.prototype.options.icon = DefaultIcon;

interface MapProps {
  messages: GPSMessage[];
  selectedMessage: GPSMessage | null;
  selectedVoyage: string | null;
  selectedImei: string;
  voyages: Voyage[];
  onMessageSelect: (message: GPSMessage) => void;
}

// Component to fit map bounds to data
const MapBounds: React.FC<{ messages: GPSMessage[] }> = ({ messages }) => {
  const map = useMap();

  useEffect(() => {
    if (messages.length > 0) {
      const bounds = new LatLngBounds(
        messages.map(msg => [msg.latitude, msg.longitude] as LatLngTuple)
      );
      
      if (bounds.isValid()) {
        map.fitBounds(bounds, { padding: [20, 20] });
      }
    }
  }, [messages, map]);

  return null;
};

export const Map: React.FC<MapProps> = ({
  messages,
  selectedMessage,
  selectedVoyage,
  selectedImei,
  voyages,
  onMessageSelect
}) => {
  const mapRef = useRef<L.Map>(null);

  // Messages are already filtered by IMEI in the parent component
  const displayMessages = messages;

  // Create polyline coordinates for GPS tracking
  const polylineCoordinates = React.useMemo(() => {
    if (selectedVoyage) {
      // Show specific voyage track
      const voyage = voyages.find(v => v.id === selectedVoyage && v.imei === selectedImei);
      if (voyage) {
        return voyage.messages
          .sort((a, b) => new Date(a.trackerTimestamp).getTime() - new Date(b.trackerTimestamp).getTime())
          .map(msg => [msg.latitude, msg.longitude] as LatLngTuple);
      }
    } else {
      // Show all tracks for selected IMEI
      return messages
        .filter(msg => msg.imei === selectedImei)
        .sort((a, b) => new Date(a.trackerTimestamp).getTime() - new Date(b.trackerTimestamp).getTime())
        .map(msg => [msg.latitude, msg.longitude] as LatLngTuple);
    }
    return [];
  }, [selectedVoyage, voyages, messages, selectedImei]);

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Inizio':
        return '#10b981'; // green
      case 'Fine':
        return '#ef4444'; // red
      case 'GPS Fixed':
        return '#4ade80'; // light green
      case 'No GPS Fixed':
        return '#f59e0b'; // orange
      default:
        return '#6b7280'; // gray
    }
  };

  return (
    <div className="h-full w-full">
      <MapContainer
        ref={mapRef}
        center={[40.7128, -74.0060]} // Default to NYC
        zoom={10}
        style={{ height: '100%', width: '100%' }}
        className="rounded"
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        <MapBounds messages={displayMessages} />
        
        {/* Draw GPS tracks */}
        {polylineCoordinates.length > 1 && (
          <Polyline
            positions={polylineCoordinates}
            color={selectedVoyage ? "#ef4444" : "#4ade80"}
            weight={selectedVoyage ? 4 : 2}
            opacity={selectedVoyage ? 0.8 : 0.6}
          />
        )}
        
        {/* Display GPS message markers */}
        {displayMessages.map((message) => {
          const isSelected = selectedMessage?.id === message.id;
          const icon = L.divIcon({
            html: `<div style="background-color: ${getStatusColor(message.status)}; width: ${isSelected ? '16px' : '12px'}; height: ${isSelected ? '16px' : '12px'}; border-radius: 50%; border: ${isSelected ? '3px' : '2px'} solid white; box-shadow: 0 2px ${isSelected ? '6px' : '4px'} rgba(0,0,0,${isSelected ? '0.4' : '0.3'});"></div>`,
            iconSize: [isSelected ? 16 : 12, isSelected ? 16 : 12],
            iconAnchor: [isSelected ? 8 : 6, isSelected ? 8 : 6],
          });

          return (
            <Marker
              key={message.id}
              position={[message.latitude, message.longitude]}
              icon={icon}
              eventHandlers={{
                click: () => onMessageSelect(message),
              }}
            >
              <Popup>
                <div className="text-sm">
                  <div className="font-semibold mb-2">GPS Position</div>
                  <div><strong>IMEI:</strong> {message.imei}</div>
                  <div><strong>Status:</strong> {message.status}</div>
                  <div><strong>Speed:</strong> {message.speed} km/h</div>
                  <div><strong>Battery:</strong> {message.batteryLevel}%</div>
                  <div><strong>Coordinates:</strong></div>
                  <div className="text-xs text-secondary">
                    {message.latitude.toFixed(6)}, {message.longitude.toFixed(6)}
                  </div>
                  <div><strong>Tracker Time:</strong></div>
                  <div className="text-xs text-secondary">
                    {formatTimestamp(message.trackerTimestamp)}
                  </div>
                  <div><strong>Server Time:</strong></div>
                  <div className="text-xs text-secondary">
                    {formatTimestamp(message.serverTimestamp)}
                  </div>
                </div>
              </Popup>
            </Marker>
          );
        })}
      </MapContainer>
    </div>
  );
};
